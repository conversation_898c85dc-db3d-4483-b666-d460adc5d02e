<?php
// Script pour vérifier les libellés dans la base de données
require_once 'main.inc.php';

echo "=== Vérification des libellés ===\n";

// Vérifier les produits
echo "\n--- Produits ---\n";
$sql = "SELECT rowid, ref, label FROM " . MAIN_DB_PREFIX . "product ORDER BY rowid LIMIT 10";
$result = $db->query($sql);
if ($result) {
    while ($obj = $db->fetch_object($result)) {
        echo "ID: {$obj->rowid}, Ref: {$obj->ref}, Label: {$obj->label}\n";
    }
} else {
    echo "Erreur: " . $db->lasterror() . "\n";
}

// Vérifier les sociétés
echo "\n--- Sociétés ---\n";
$sql = "SELECT rowid, nom, name_alias FROM " . MAIN_DB_PREFIX . "societe ORDER BY rowid LIMIT 10";
$result = $db->query($sql);
if ($result) {
    while ($obj = $db->fetch_object($result)) {
        echo "ID: {$obj->rowid}, Nom: {$obj->nom}, Alias: {$obj->name_alias}\n";
    }
} else {
    echo "Erreur: " . $db->lasterror() . "\n";
}

// Vérifier les commandes
echo "\n--- Commandes ---\n";
$sql = "SELECT rowid, ref, fk_soc FROM " . MAIN_DB_PREFIX . "commande ORDER BY rowid LIMIT 10";
$result = $db->query($sql);
if ($result) {
    while ($obj = $db->fetch_object($result)) {
        echo "ID: {$obj->rowid}, Ref: {$obj->ref}, Société: {$obj->fk_soc}\n";
    }
} else {
    echo "Erreur: " . $db->lasterror() . "\n";
}

// Vérifier les traductions
echo "\n--- Traductions ---\n";
$sql = "SELECT transkey, transvalue FROM " . MAIN_DB_PREFIX . "overwrite_trans WHERE lang = 'fr_FR' AND transvalue LIKE '%text to show%' LIMIT 10";
$result = $db->query($sql);
if ($result) {
    $count = $db->num_rows($result);
    echo "Nombre de traductions contenant 'text to show': $count\n";
    while ($obj = $db->fetch_object($result)) {
        echo "Clé: {$obj->transkey}, Valeur: {$obj->transvalue}\n";
    }
} else {
    echo "Erreur ou table inexistante: " . $db->lasterror() . "\n";
}

// Vérifier la configuration de langue
echo "\n--- Configuration langue ---\n";
echo "Langue par défaut: " . $langs->getDefaultLang() . "\n";
echo "Langue courante: " . $langs->defaultlang . "\n";

// Vérifier les constantes de configuration
echo "\n--- Constantes de configuration ---\n";
$sql = "SELECT name, value FROM " . MAIN_DB_PREFIX . "const WHERE name LIKE '%LANG%' OR name LIKE '%TRANSLATION%'";
$result = $db->query($sql);
if ($result) {
    while ($obj = $db->fetch_object($result)) {
        echo "{$obj->name}: {$obj->value}\n";
    }
} else {
    echo "Erreur: " . $db->lasterror() . "\n";
}
?>
